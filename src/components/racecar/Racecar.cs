using System;
using Godot;

public partial class Racecar : CharacterBody2D
{
    [Export]
    public Marker2D StartPosition;
    private float CarHeading = 0f;
    private float SteerAngle = 0f;

    private Vector2 Acceleration = Vector2.Zero;

    private float EnginePower = 125f;

    private float BrakePower = 50f;

    private float Friction = -0.1f;
    private float Drag = -0.0009f;

    private float SlipSpeed = 200f;
    private float TractionWhenSlipping = 0.05f;
    private float TractionWhenGripping = 1f;

    private float StallTime = 0.5f;
    private bool isStalled = false;
    private float StallCountdown = 0f;
    private bool isCrashed = false;

    private Marker2D FrontWheel;
    private Marker2D BackWheel;

    private Sprite2D CarSprite;

    private enum AnimateTurn
    {
        None = 0,
        Left = 1,
        Right = 2,
    }


    public override void _Ready()
    {
        FrontWheel = GetNode<Marker2D>("Front");
        BackWheel = GetNode<Marker2D>("Back");
        CarSprite = GetNode<Sprite2D>("Sprite2D");

        Reset();
    }

    public override void _PhysicsProcess(double delta)
    {
        if (StallCountdown > 0)
        {
            StallCountdown -= (float)delta;
            if (StallCountdown <= 0)
            {
                isStalled = false;
                if (isCrashed)
                {
                    Reset();
                }
            }

            if (isStalled)
            {
                MoveAndSlide();
                return;
            }
        }

        int turn = 0;
        if (Input.IsActionPressed("steer_right"))
        {
            turn = 1;
            CarSprite.SetFrame((int)AnimateTurn.Right);
        }
        if (Input.IsActionPressed("steer_left"))
        {
            turn = -1;
            CarSprite.SetFrame((int)AnimateTurn.Left);
        }

        if (turn == 0)
        {
            CarSprite.SetFrame((int)AnimateTurn.None);
        }

        bool isCoasting = true;
        if (Input.IsActionPressed("accelerate"))
        {
            Acceleration = Transform.X * EnginePower;
            isCoasting = false;
        }

        if (Input.IsActionPressed("brake-reverse"))
        {
            Acceleration = -1 * Transform.X * BrakePower;
            isCoasting = false;
        }

        if (isCoasting)
        {
            Acceleration = Vector2.Zero;
        }

        Vector2 frictionForce = Velocity * Friction;
        Vector2 dragForce = Velocity * Velocity.Length() * Drag;

        Acceleration += frictionForce + dragForce;


        SteerAngle = turn * Mathf.DegToRad(0.5f);

        Vector2 backWheel = BackWheel.GlobalPosition + Velocity;
        Vector2 frontWheel = FrontWheel.GlobalPosition + Velocity.Rotated(SteerAngle);
        Vector2 newHeading = backWheel.DirectionTo(frontWheel);

        float traction = TractionWhenGripping;
        if (turn != 0 && Velocity.Length() > SlipSpeed)
        {
            traction = TractionWhenSlipping;
        }

        float newHeadingDot = newHeading.Dot(Velocity.Normalized());
        if (newHeadingDot > 0)
        {
            Velocity = Velocity.Lerp(newHeading * Velocity.Length(), traction);
        }
        else
        {
            Velocity = Velocity.Lerp(-1 * newHeading * Velocity.Length(), traction);
        }

        Velocity += Acceleration;
        Rotation = newHeading.Angle();

        GD.Print(Velocity.Length());

        KinematicCollision2D collision = MoveAndCollide(Velocity * (float)delta);

        if (collision != null)
        {
            GodotObject collider = collision.GetCollider();
            if (collider is StaticBody2D staticBody)
            {
                isStalled = true;
                StallCountdown = StallTime;
                if (staticBody.Name == "CrashObstacles")
                {
                    GD.Print("Crash!");
                    isCrashed = true;
                }
                else if (staticBody.Name == "BounceObstacles")
                {
                    Velocity = collision.GetNormal() * EnginePower;
                }
            }
        }
    }

    private void Reset()
    {
        GlobalPosition = StartPosition.GlobalPosition;
        Rotation = StartPosition.Rotation;
        Velocity = Vector2.Zero;
        Acceleration = Vector2.Zero;
        SteerAngle = 0;
        isCrashed = false;
        isStalled = false;
        StallCountdown = 0;
    }
}
