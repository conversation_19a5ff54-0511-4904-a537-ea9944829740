[gd_scene load_steps=7 format=3 uid="uid://7o8wv0nvc7p7"]

[ext_resource type="Texture2D" uid="uid://cit0dfy118dyi" path="res://assets/sprites/test-track.png" id="1_ebpgw"]
[ext_resource type="Texture2D" uid="uid://qhf204tgjq6v" path="res://assets/sprites/test-car.png" id="2_dql50"]
[ext_resource type="Script" uid="uid://4obiongrg288" path="res://src/components/racecar/Racecar.cs" id="2_scayd"]

[sub_resource type="CapsuleShape2D" id="CapsuleShape2D_dql50"]
radius = 12.0
height = 40.0

[sub_resource type="CircleShape2D" id="CircleShape2D_scayd"]
radius = 23.0

[sub_resource type="CapsuleShape2D" id="CapsuleShape2D_scayd"]
radius = 6.0
height = 46.0

[node name="Demo Track" type="Node2D"]

[node name="Test-track" type="Sprite2D" parent="."]
position = Vector2(0, -5)
texture = ExtResource("1_ebpgw")

[node name="Racecar" type="CharacterBody2D" parent="." node_paths=PackedStringArray("StartPosition")]
script = ExtResource("2_scayd")
StartPosition = NodePath("../StartPosition")

[node name="Sprite2D" type="Sprite2D" parent="Racecar"]
rotation = 1.5708
texture = ExtResource("2_dql50")
hframes = 3

[node name="Camera2D" type="Camera2D" parent="Racecar"]
zoom = Vector2(2, 2)
drag_horizontal_enabled = true
drag_vertical_enabled = true
drag_left_margin = 0.4
drag_top_margin = 0.4
drag_right_margin = 0.4
drag_bottom_margin = 0.4

[node name="CollisionShape2D" type="CollisionShape2D" parent="Racecar"]
rotation = 1.5708
shape = SubResource("CapsuleShape2D_dql50")

[node name="Front" type="Marker2D" parent="Racecar"]
position = Vector2(20, 0)

[node name="Back" type="Marker2D" parent="Racecar"]
position = Vector2(-10, 0)

[node name="CrashObstacles" type="StaticBody2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="CrashObstacles"]
position = Vector2(239, -39)
shape = SubResource("CircleShape2D_scayd")

[node name="BounceObstacles" type="StaticBody2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="BounceObstacles"]
position = Vector2(-122, 68)
rotation = 1.5708
shape = SubResource("CapsuleShape2D_scayd")

[node name="StartPosition" type="Marker2D" parent="."]
position = Vector2(174, 101)
rotation = 1.5708
